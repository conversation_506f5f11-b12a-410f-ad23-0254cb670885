{"name": "getrecon.xyz", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint src/. --ext ts --ext tsx --ext js --fix", "prettier": "prettier --write ."}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/react-fontawesome": "^0.2.0", "@hookform/resolvers": "^3.3.4", "@octokit/app": "^14.0.2", "@recon-fuzz/abi-to-invariants-ts": "^2.0.0", "@recon-fuzz/log-parser": "0.0.35", "@sentry/nextjs": "^7.101.0", "@solidity-parser/parser": "^0.19.0", "@tailwindcss/typography": "^0.5.10", "@tanstack/react-query": "^5.15.4", "@types/react-syntax-highlighter": "^15.5.13", "axios": "^1.6.2", "babel-plugin-react-compiler": "^19.1.0-rc.3", "clsx": "^2.1.0", "ethers": "^6.13.4", "evmole": "^0.7.2", "file-saver": "^2.0.5", "jszip": "^3.10.1", "lodash": "^4.17.21", "next": "15.5.2", "next-auth": "^4.24.5", "next-themes": "^0.2.1", "ogl": "^1.0.11", "oz-roles-scraper": "^1.1.0", "postcss": "^8.4.35", "prismjs": "^1.29.0", "react": "^19", "react-code-blocks": "^0.1.6", "react-dom": "^19", "react-hook-form": "^7.50.1", "react-hot-toast": "^2.5.1", "react-icons": "^5.0.1", "react-syntax-highlighter": "^15.6.6", "react-tweet": "^3.2.1", "sass": "^1.69.6", "tailwind-merge": "^2.2.1", "tailwind-nord": "^1.3.0", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.1", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^6.19.1", "autoprefixer": "^10.4.17", "eslint": "^8", "eslint-config-next": "15.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-tailwindcss": "^3.14.0", "prettier": "3.1.1", "react-toastify": "^10.0.4", "typescript": "^5"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}