import React from "react";

interface TrophyProps {
  project: string;
  severity: string;
  findingName: string;
  findingUrl: string;
  reconLogs: string;
  description: string;
};

export default function Trophy({
  project,
  severity,
  findingName,
  findingUrl,
  reconLogs,
  description
}: TrophyProps) {
  return (
    <div
      className="mr-7 min-h-[272px] w-[300px] flex-none snap-center rounded-lg bg-[#6750A41F] from-purple-800 to-gray-900 p-4 text-white shadow-lg md:w-[370px] lg:w-[370px]"
    >
      <div className="flex h-full flex-col justify-between">
        <div className="grid grid-cols-[3fr_1fr] items-start gap-4">
          <div>
            <p className="text-left text-2xl font-bold">{project}</p>
            <p className="text-left text-lg font-semibold">
              {severity} | {findingName}
            </p>
            <a
              href={findingUrl}
              target="_blank"
              rel="noreferrer"
              className="text-lg font-semibold text-[#D0BCFF] underline"
            >
              Finding
            </a>{" "}
            |{" "}
            <a
              href={reconLogs}
              target="_blank"
              rel="noreferrer"
              className="text-lg font-semibold text-[#D0BCFF] underline"
            >
              Recon Logs
            </a>
          </div>
          <div className="flex items-start justify-center">
            <span className="flex size-12 items-center justify-center rounded-full bg-[#D0BCFF29] text-2xl">
              🏆
            </span>
          </div>
        </div>

        <div className=" mt-4 flex flex-col justify-center ">
          <p className="text-xl">{description}</p>
          <a
            href={findingUrl}
            target="_blank"
            rel="noreferrer"
            className="block text-lg font-semibold text-[#D0BCFF] underline"
          >
            BUG LINK &gt;
          </a>
        </div>
      </div>
    </div>
  );
}
