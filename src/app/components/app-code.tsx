import { memo } from "react";
import { CopyBlock } from "react-code-blocks";

import { useCodeBlockTheme } from "../services/code-block-utils";

type AppCodeProps = {
  code: string;
  language: string;
  limit?: number;
  showLineNumbers?: boolean;
  maxHeight?: number;
  extraCss?: string;
};

export const AppCode = memo(
  ({
    code,
    language,
    limit = 1500,
    showLineNumbers = true,
    maxHeight,
    extraCss = "",
    // Filter out the 'copied' prop that might be passed incorrectly
    copied: _copied,
  }: AppCodeProps & { copied?: any }) => {
    const theme = useCodeBlockTheme();

    return (
      <div
        className={
          maxHeight ? `h-[700px] mb-5 ${extraCss}` : `mb-5 ${extraCss}`
        }
      >
        <CopyBlock
          text={code}
          language={language}
          theme={theme}
          customStyle={{
            overflowX: "auto",
            maxHeight: maxHeight ? `${maxHeight}px` : undefined,
          }}
          showLineNumbers={showLineNumbers}
        />
      </div>
    );
  }
);

AppCode.displayName = "AppCode";
