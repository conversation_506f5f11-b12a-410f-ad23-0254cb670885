"use client";

import axios from "axios";
import Link from "next/link";
import { useState } from "react";

import { AppInput } from "@/app/components/app-input";
import { AppListCheckItem } from "@/app/components/app-list-check-item";
import { AppButton } from "@/app/components/app-button";
import { PageHeader } from "@/app/components/page-header";
import { useBooleanState } from "@/app/services/helpers";
import { OrgStatus, useGetMyOrg } from "@/app/services/organization.hooks";

export default function Onboard() {
  const [inviteCode, setInviteCode] = useState("");
  const [isLoading, { setTrue: setLoading, setFalse: unsetLoading }] =
    useBooleanState(false);

  const {
    isLoading: isOrgLoading,
    refetch: refetchOrg,
    orgStatus,
  } = useGetMyOrg();

  const createFreeOrg = async () => {
    if (isLoading) {
      return;
    }

    setLoading();
    try {
      const createdOrg = await axios({
        method: "POST",
        url: `/api/organizations/`,
      });

      console.log("createdOrg", createdOrg);
      refetchOrg();
    } catch (e) {
      alert(`Something went wrong: ${e.response.data.message}`);
      console.log("e", e);
    }
    unsetLoading();
  };

  const createWithCode = async (e) => {
    if (isLoading) {
      return;
    }
    e.preventDefault();

    try {
      const createdOrg = await axios({
        method: "POST",
        url: `/api/organizations/`,
        data: {
          inviteCode,
        },
      });

      console.log("createdOrg", createdOrg);
      refetchOrg();
    } catch (e) {
      alert(`Something went wrong: ${e.response.data.message}`);
      console.log("e", e);
    }

    console.log("inviteCode", inviteCode);
  };

  const renderListItem = (text) => {
    return <AppListCheckItem text={text} key={text} />;
  };

  // TODO: At end of calls, trigger update of ORG!

  return (
    <div className="pl-[80px] pt-[40px]">
      {orgStatus === OrgStatus.NOORG && (
        <div className="text-fore-neutral-primary">
          <PageHeader title="Create a Recon Organization!" descriptions={[]} />

          <div className="text-[16px] leading-[26px]">
            <p className="my-[20px] max-w-[500px]">
              An Account belongs to an Organization.You can have a Free or Paid
              Org. If you belong to a Paid Org, please get a Invite Code, and
              add it.NOTE: You can always be added to the org later via a
              inviteCode, or by a super admin
              <span className="mt-[10px]">
                By creating an account you're agreeing to our{" "}
                <Link href="/legal/fair-usage">fair usage policies</Link>
              </span>
            </p>
          </div>

          <ul className="text-[16px] leading-[26px]">
            {[
              "RECON FREE for Public Goods!",
              "Public Repositories can build Handlers for Free!",
              "Update ABI Data on new commits",
              "Automatically re-build handlers",
              "Export files and run them locally!",
            ].map(renderListItem)}
          </ul>
          <AppButton
            disabled={isLoading}
            onClick={createFreeOrg}
            className="mt-[26px] w-[233px] justify-center"
          >
            {isLoading && <>Loading</>}
            {!isLoading && <>Join for Free!</>}
          </AppButton>
          <div className="mt-[40px]">
            <h3 className="text-[21px] leading-[28px]">RECON PRO</h3>
            <p className="text-[21px] leading-[28px]">
              Pro Organizations can build both Public and Private Repos and Much
              More
            </p>
            <ul className="text-[16px] leading-[26px]">
              {[
                "Add Public and Private Repos to your Organization and Users",
                "Run multiple fuzzers in the cloud, simultaneously",
                "Automated Runs on PR or Commit",
                "Automated Broken Invariants Test Generation",
                "Advanced Builder",
                "Store Recipes of your common jobs",
                "One click shareable Job Reports for your SRs",
                "Private Coaching",
              ].map(renderListItem)}
            </ul>
            <form
              onSubmit={createWithCode}
              className="my-[40px] flex items-center gap-[20px]"
            >
              <AppInput
                value={inviteCode}
                onChange={(e) => setInviteCode(e.target.value)}
              />
              <AppButton disabled={isLoading} type="submit">
                {isLoading && <>Loading</>}{" "}
                {!isLoading && <>I have an invite Code!</>}
              </AppButton>
            </form>
            <p className="pb-[20px] text-[21px] leading-[28px]">
              No Code? Talk to us!
            </p>
          </div>
        </div>
      )}
      {!isOrgLoading && orgStatus !== OrgStatus.NOORG && (
        <div>
          <PageHeader title="Welcome to Recon!" descriptions={[]} />
          <p className="my-[20px] max-w-[500px] text-fore-neutral-secondary">
            <Link href="/dashboard/build">
              <AppButton>Build your First Repo</AppButton>
            </Link>
          </p>
          <div className="text-[35px] text-fore-neutral-primary">
            <h2 className="mt-[25px]">Demo</h2>
            <iframe
              width="560"
              height="315"
              src="https://www.youtube.com/embed/z0sktBuJfEI"
              title=""
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowFullScreen
            ></iframe>

            <h2 className="mt-[25px]">5 Minutes Tutorial</h2>
            <iframe
              width="560"
              height="315"
              src="https://www.youtube.com/embed/RwfiPrxbdBg"
              title=""
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowFullScreen
            ></iframe>
          </div>
        </div>
      )}
    </div>
  );
}
