"use client";
import { AppPageTitle } from "@/app/components/app-page-title";
import { AppTextarea } from "@/app/components/app-textarea";
import Footer from "@/app/components/Footer/Footer";
import { useMemo, useState } from "react";
import { AppCode } from "@/app/components/app-code";
import { Body2, H3 } from "@/app/components/app-typography";
import { AppHeader } from "@/app/(app)/components/app-header";

function stringToHex(input: string, padToBytes: number = 0): string {
  // Convert string to UTF-8 bytes
  const encoder = new TextEncoder();
  const bytes = encoder.encode(input);

  // Convert bytes to hex
  const hex = Array.from(bytes)
    .map((byte) => byte.toString(16).padStart(2, "0"))
    .join("");

  // Add length prefix (number of bytes)
  const lengthHex = bytes.length.toString(16).padStart(2, "0");
  const result = `0x${lengthHex}${hex}`;

  // Pad if requested
  if (padToBytes > 0) {
    const currentLength = result.length - 2; // Subtract '0x'
    const paddingNeeded = padToBytes * 2 - currentLength;
    if (paddingNeeded > 0) {
      return result + "0".repeat(paddingNeeded);
    }
  }

  return result;
}

export default function BytecodeStringToolInternal() {
  const [string, setString] = useState("");

  const encoded = useMemo(() => {
    return stringToHex(string);
  }, [string]);

  const padded = useMemo(() => {
    return stringToHex(string, 32);
  }, [string]);

  return (
    <div className="min-h-screen bg-back-neutral-secondary dark:bg-back-neutral-primary">
      <AppHeader skipUser />
      <div className="min-h-[800px] p-[45px]">
        <div className="mb-[20px]">
          <AppPageTitle className="mb-[3px]">Bytecode String Tool</AppPageTitle>

          <Body2 className="mb-[3px]">
            This tool encodes strings into UTF-8 Bytes
          </Body2>
          <Body2 className="mb-[3px]">
            It also adds a length value as a prefix and pads the length to 32
            bytes
          </Body2>
        </div>
        <div className="grid grid-cols-2 gap-10">
          <div className="flex flex-col">
            <AppTextarea
              className="mb-[8px]"
              label={"String (Max 32 chars)"}
              value={string}
              onChange={(e) => setString(e.target.value)}
            />
          </div>
        </div>

        <H3 className="text-fore-neutral-primary">Encoded</H3>
        <AppCode showLineNumbers={false} code={encoded} language="python" />
        <H3 className="text-fore-neutral-primary">Encoded and Padded</H3>
        <AppCode showLineNumbers={false} code={padded} language="python" />
      </div>
      <Footer />
    </div>
  );
}
